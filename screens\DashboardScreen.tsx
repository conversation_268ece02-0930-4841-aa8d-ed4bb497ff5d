
import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { MOCK_JOBS } from '../data/mockData';
import { Card, CardHeader } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { theme } from '../styles/theme';
import { BriefcaseIcon, ClipboardListIcon, TrendingUpIcon } from '../components/ui/icons';
import { ApplicationTracker } from '../components/ApplicationTracker';
import { SkillTrends } from '../components/SkillTrends';

const DashboardScreen = ({ navigation }: { navigation: any }) => {
  const newJobs = MOCK_JOBS.slice(0, 3);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Welcome Back!</Text>
        <Text style={styles.subtitle}>Here's your job hunting snapshot.</Text>
      </View>

      <Card style={styles.card}>
        <CardHeader title="New Matched Jobs" />
        <View style={styles.jobList}>
          {newJobs.map(job => (
            <View key={job.id} style={styles.jobItem}>
              <Text style={styles.jobTitle}>{job.title}</Text>
              <Text style={styles.jobCompany}>{job.company}</Text>
              <View style={styles.jobFooter}>
                  <Text style={styles.matchScore}>{job.matchScore}% Match</Text>
                  <Text style={styles.jobPosted}>{job.postedAt}</Text>
              </View>
            </View>
          ))}
        </View>
        <Button title="View All Jobs" onPress={() => navigation.navigate('Automation Feed')} />
      </Card>
      
      <Card style={styles.card}>
        <CardHeader title="Top Skill Trends" />
        <SkillTrends isDashboard={true} />
      </Card>

      <Card style={styles.card}>
         <CardHeader title="Application Pipeline" rightComponent={
            <TouchableOpacity onPress={() => navigation.navigate('Applications')}>
                <Text style={styles.linkText}>View Board</Text>
            </TouchableOpacity>
         } />
        <ApplicationTracker isDashboard={true}/>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.brand.primary,
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text.primary,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.text.secondary,
    marginTop: 4,
  },
  card: {
    marginBottom: 24,
  },
  jobList: {
    marginBottom: 16,
  },
  jobItem: {
    backgroundColor: theme.colors.slate[800],
    padding: 12,
    borderRadius: theme.borderRadius.md,
    marginBottom: 12,
  },
  jobTitle: {
    color: theme.colors.text.primary,
    fontWeight: 'bold',
    fontSize: 16,
  },
  jobCompany: {
    color: theme.colors.text.secondary,
    fontSize: 14,
  },
  jobFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 8,
  },
  matchScore: {
      color: theme.colors.green[400],
      fontWeight: 'bold',
      fontSize: 12,
  },
  jobPosted: {
      color: theme.colors.text.secondary,
      fontSize: 12,
  },
  linkText: {
    color: theme.colors.brand.accent,
    fontWeight: 'bold',
  }
});

export default DashboardScreen;
