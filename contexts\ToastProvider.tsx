import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { CheckCircleIcon, XCircleIcon, InformationCircleIcon } from '../components/ui/icons';

export interface ToastMessage {
  id: number;
  message: string;
  type: 'success' | 'error' | 'info';
}

export interface ToastContextType {
  show: (options: { message: string; type: 'success' | 'error' | 'info' }) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

const Toast: React.FC<{ message: ToastMessage; onDismiss: (id: number) => void }> = ({ message, onDismiss }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onDismiss(message.id);
    }, 5000); // Auto-dismiss after 5 seconds

    return () => clearTimeout(timer);
  }, [message, onDismiss]);

  const icons = {
    success: <CheckCircleIcon className="h-6 w-6 text-green-400" />,
    error: <XCircleIcon className="h-6 w-6 text-red-400" />,
    info: <InformationCircleIcon className="h-6 w-6 text-sky-400" />,
  };
  
  const colors = {
    success: 'border-green-500',
    error: 'border-red-500',
    info: 'border-sky-500'
  }

  return (
    <div 
        className={`w-full max-w-sm p-4 bg-brand-secondary rounded-lg shadow-lg flex items-center space-x-4 border-l-4 ${colors[message.type]}`}
        role="alert"
    >
      <div>{icons[message.type]}</div>
      <p className="flex-1 text-brand-text">{message.message}</p>
      <button onClick={() => onDismiss(message.id)} className="text-brand-text-secondary">
        <XCircleIcon className="h-5 w-5" />
      </button>
    </div>
  );
};

export const ToastContainer: React.FC<{ toasts: ToastMessage[]; onDismiss: (id: number) => void }> = ({ toasts, onDismiss }) => {
    return (
        <div className="fixed top-5 right-5 z-50 space-y-3">
            {toasts.map(toast => (
                <Toast key={toast.id} message={toast} onDismiss={onDismiss} />
            ))}
        </div>
    );
};

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  const show = useCallback(({ message, type }: { message: string; type: 'success' | 'error' | 'info' }) => {
    const newToast = { id: Date.now(), message, type };
    setToasts(currentToasts => [...currentToasts, newToast]);
  }, []);

  const dismiss = useCallback((id: number) => {
    setToasts(currentToasts => currentToasts.filter(toast => toast.id !== id));
  }, []);

  return (
    <ToastContext.Provider value={{ show }}>
      {children}
      <ToastContainer toasts={toasts} onDismiss={dismiss} />
    </ToastContext.Provider>
  );
};
