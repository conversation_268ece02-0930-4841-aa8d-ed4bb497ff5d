
import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, ScrollView } from 'react-native';
import { MOCK_RESUME_TEMPLATES } from '../data/mockData';
import { ResumeTemplate } from '../types';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { useToast } from '../contexts/ToastProvider';
import { theme } from '../styles/theme';

const TemplateCard: React.FC<{
  template: ResumeTemplate;
  onSetDefault: (id: string) => void;
  onDelete: (id: string) => void;
}> = ({ template, onSetDefault, onDelete }) => {
  const toast = useToast();

  return (
    <Card style={styles.templateCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>{template.name}</Text>
          {template.isDefault && (
            <View style={styles.defaultBadge}>
                <Text style={styles.defaultBadgeText}>DEFAULT</Text>
            </View>
          )}
        </View>
        <Text style={styles.cardInfo}>Base Score: {template.baseScore}%</Text>
        <Text style={styles.cardContent} numberOfLines={4}>
          {template.content.trim()}
        </Text>
      <View style={styles.buttonContainer}>
        <Button
          title="Set Default"
          onPress={() => onSetDefault(template.id)}
          disabled={template.isDefault}
          variant="secondary"
          style={{ flex: 1 }}
        />
        <Button 
            title="Delete"
            variant="secondary" 
            onPress={() => onDelete(template.id)} 
            style={{ flex: 1, backgroundColor: theme.colors.red[400] + '30' }}
            textStyle={{ color: theme.colors.red[400] }}
            />
      </View>
    </Card>
  );
};


const ResumeTemplatesScreen: React.FC = () => {
  const [templates, setTemplates] = useState<ResumeTemplate[]>(MOCK_RESUME_TEMPLATES);
  const toast = useToast();

  const handleSetDefault = (id: string) => {
    const newTemplates = templates.map(t => ({ ...t, isDefault: t.id === id }));
    const newDefault = newTemplates.find(t => t.isDefault);
    setTemplates(newTemplates);
    if (newDefault) {
      toast.show({type: 'success', message: `"${newDefault.name}" is now default.`});
    }
  };

  const handleDelete = (id: string) => {
    const toDelete = templates.find(t => t.id === id);
    if(toDelete?.isDefault){
      toast.show({type: 'error', message: 'Cannot delete default resume.'});
      return;
    }
    setTemplates(templates.filter(t => t.id !== id));
    if(toDelete) {
        toast.show({type: 'success', message: `Deleted "${toDelete.name}".`});
    }
  };

  return (
    <FlatList
        data={templates}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
            <TemplateCard
                template={item}
                onSetDefault={handleSetDefault}
                onDelete={handleDelete}
            />
        )}
        contentContainerStyle={styles.container}
    />
  );
};

const styles = StyleSheet.create({
    container: {
        padding: 16,
    },
    templateCard: {
        marginBottom: 16,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    cardTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: theme.colors.text.primary,
    },
    defaultBadge: {
        backgroundColor: theme.colors.brand.accent,
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: theme.borderRadius.full,
    },
    defaultBadgeText: {
        color: theme.colors.brand.primary,
        fontSize: 10,
        fontWeight: 'bold',
    },
    cardInfo: {
        fontSize: 14,
        color: theme.colors.text.secondary,
        marginBottom: 8,
    },
    cardContent: {
        fontSize: 12,
        color: theme.colors.text.secondary,
        fontStyle: 'italic',
        lineHeight: 18,
        marginBottom: 16,
    },
    buttonContainer: {
        flexDirection: 'row',
        gap: 8,
    }
});

export default ResumeTemplatesScreen;
