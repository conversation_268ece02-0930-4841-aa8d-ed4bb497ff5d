
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MOCK_APPLICATIONS } from '../data/mockData';
import { KANBAN_ORDER } from '../constants';
import { theme } from '../styles/theme';
import { ApplicationStatus } from '../types';

const STATUS_COLORS: { [key in ApplicationStatus]?: string } = {
  [ApplicationStatus.Queued]: theme.colors.slate[500],
  [ApplicationStatus.AwaitingApproval]: theme.colors.amber[400],
  [ApplicationStatus.AutoApplying]: theme.colors.sky[300],
  [ApplicationStatus.Applying]: theme.colors.sky[300],
  [ApplicationStatus.Submitted]: theme.colors.brand.accent,
  [ApplicationStatus.Interview]: theme.colors.green[400],
  [ApplicationStatus.Offer]: theme.colors.green[400],
  [ApplicationStatus.Rejected]: theme.colors.red[400],
};

export const ApplicationTracker: React.FC<{ isDashboard?: boolean }> = ({ isDashboard }) => {
  if (!isDashboard) {
    return null;
  }

  const statusCounts = KANBAN_ORDER.map(status => ({
    status,
    count: MOCK_APPLICATIONS.filter(app => app.status === status).length
  }));
  
  const totalApps = statusCounts.reduce((sum, item) => sum + item.count, 0);

  if (totalApps === 0) {
    return <Text style={styles.legendText}>No applications in the pipeline yet.</Text>;
  }

  return (
    <View>
      <View style={styles.pipelineBar}>
        {statusCounts.filter(s => s.count > 0).map(({ status, count }) => (
          <View
            key={status}
            style={{
              width: `${(count / totalApps) * 100}%`,
              backgroundColor: STATUS_COLORS[status] || theme.colors.slate[600],
            }}
          />
        ))}
      </View>
      <View style={styles.legendContainer}>
        {statusCounts.filter(s => s.count > 0).map(({ status, count }) => (
          <View key={status} style={styles.legendItem}>
            <View style={[styles.legendIndicator, { backgroundColor: STATUS_COLORS[status] || theme.colors.slate[600] }]} />
            <Text style={styles.legendText}>{status} ({count})</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  pipelineBar: {
    height: 20,
    flexDirection: 'row',
    borderRadius: theme.borderRadius.md,
    overflow: 'hidden',
    backgroundColor: theme.colors.slate[700],
    marginBottom: 16,
  },
  legendContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendIndicator: {
    width: 10,
    height: 10,
    borderRadius: 2,
    marginRight: 6,
  },
  legendText: {
    color: theme.colors.text.secondary,
    fontSize: 12,
  }
});
