
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Modal, SafeAreaView } from 'react-native';
import { MOCK_APPLICATIONS } from '../data/mockData';
import { Application, ApplicationStatus, LogEntry } from '../types';
import { Card } from '../components/ui/Card';
import { KANBAN_ORDER } from '../constants';
import { theme } from '../styles/theme';
import { CloseIcon } from '../components/ui/icons';

const ApplicationCard: React.FC<{ application: Application, onPress: () => void }> = ({ application, onPress }) => (
    <TouchableOpacity onPress={onPress}>
        <Card style={styles.applicationCard}>
            <Text style={styles.cardTitle}>{application.job.title}</Text>
            <Text style={styles.cardCompany}>{application.job.company}</Text>
        </Card>
    </TouchableOpacity>
);

const ApplicationDetailModal: React.FC<{ application: Application | null, visible: boolean, onClose: () => void }> = ({ application, visible, onClose }) => {
    if (!application) return null;

    return (
        <Modal
            animationType="slide"
            transparent={true}
            visible={visible}
            onRequestClose={onClose}
        >
            <View style={styles.modalContainer}>
                <SafeAreaView style={{ flex: 1 }}>
                    <Card style={styles.modalContent}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>{application.job.title}</Text>
                            <TouchableOpacity onPress={onClose}><CloseIcon color={theme.colors.text.secondary} /></TouchableOpacity>
                        </View>
                        <ScrollView>
                            <Text style={styles.modalCompany}>{application.job.company}</Text>
                            <Text style={styles.modalStatus}>Status: {application.status}</Text>
                            <Text style={styles.logsHeader}>Agent Logs</Text>
                            {application.logs?.map((log, index) => (
                                <View key={index} style={styles.logEntry}>
                                    <Text style={styles.logTimestamp}>{new Date(log.timestamp).toLocaleString()}</Text>
                                    <Text style={styles.logMessage}>{log.message}</Text>
                                </View>
                            ))}
                        </ScrollView>
                    </Card>
                </SafeAreaView>
            </View>
        </Modal>
    );
};

export const ApplicationTrackerScreen: React.FC = () => {
    const [selectedApp, setSelectedApp] = useState<Application | null>(null);

    return (
        <>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.container}>
                {KANBAN_ORDER.map(status => (
                    <View key={status} style={styles.column}>
                        <Text style={styles.columnTitle}>{status}</Text>
                        <ScrollView>
                            {MOCK_APPLICATIONS
                                .filter(app => app.status === status)
                                .map(app => <ApplicationCard key={app.id} application={app} onPress={() => setSelectedApp(app)} />)
                            }
                        </ScrollView>
                    </View>
                ))}
            </ScrollView>
            <ApplicationDetailModal application={selectedApp} visible={!!selectedApp} onClose={() => setSelectedApp(null)} />
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: theme.colors.brand.primary,
        padding: 8,
    },
    column: {
        width: 280,
        backgroundColor: theme.colors.brand.secondary,
        marginHorizontal: 8,
        borderRadius: theme.borderRadius.lg,
        padding: 12,
        height: '100%',
    },
    columnTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: theme.colors.text.secondary,
        marginBottom: 16,
    },
    applicationCard: {
        marginBottom: 12,
        backgroundColor: theme.colors.slate[800],
    },
    cardTitle: {
        fontWeight: 'bold',
        color: theme.colors.text.primary,
    },
    cardCompany: {
        fontSize: 12,
        color: theme.colors.text.secondary,
    },
    modalContainer: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: 'rgba(0,0,0,0.7)',
    },
    modalContent: {
        height: '60%',
        backgroundColor: theme.colors.brand.secondary,
        borderTopLeftRadius: theme.borderRadius.lg,
        borderTopRightRadius: theme.borderRadius.lg,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingBottom: 16,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.slate[700],
    },
    modalTitle: {
        fontSize: 22,
        fontWeight: 'bold',
        color: theme.colors.text.primary,
        flex: 1,
    },
    modalCompany: {
        fontSize: 16,
        color: theme.colors.text.secondary,
        marginTop: 8,
    },
    modalStatus: {
        fontSize: 14,
        color: theme.colors.brand.accent,
        fontWeight: 'bold',
        marginTop: 16,
        marginBottom: 16,
    },
    logsHeader: {
        fontSize: 18,
        fontWeight: 'bold',
        color: theme.colors.text.secondary,
        marginBottom: 8,
    },
    logEntry: {
        backgroundColor: theme.colors.slate[800],
        padding: 10,
        borderRadius: theme.borderRadius.sm,
        marginBottom: 8,
    },
    logTimestamp: {
        fontSize: 10,
        color: theme.colors.text.secondary,
        marginBottom: 4,
    },
    logMessage: {
        fontSize: 14,
        color: theme.colors.text.primary,
    },
});

export default ApplicationTrackerScreen;