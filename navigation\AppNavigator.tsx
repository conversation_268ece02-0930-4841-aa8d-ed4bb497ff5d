
import React, { useState } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import LoginScreen from '../screens/LoginScreen';
import RegisterScreen from '../screens/RegisterScreen';
import MainDrawerNavigator from './DrawerNavigator';

export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
};

const Stack = createNativeStackNavigator();

const AuthStack = () => (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="Register" component={RegisterScreen} />
    </Stack.Navigator>
);


const AppNavigator = () => {
    // In a real app, this would be managed by a context or state manager
    const [isAuthenticated, setIsAuthenticated] = useState(false); 

    const handleLoginSuccess = () => setIsAuthenticated(true);
    const handleLogout = () => setIsAuthenticated(false);
    
    // For now, let's provide a way to toggle auth for demonstration
    // In a real app, LoginScreen would call handleLoginSuccess
    // We can simulate this by making the whole auth stack a conceptual group
    if (!isAuthenticated) {
        return (
             <Stack.Navigator screenOptions={{ headerShown: false }}>
                <Stack.Screen name="Login">
                    {props => <LoginScreen {...props} onLoginSuccess={handleLoginSuccess} />}
                </Stack.Screen>
                <Stack.Screen name="Register" component={RegisterScreen} />
            </Stack.Navigator>
        );
    }

    return <MainDrawerNavigator onLogout={handleLogout} />;
};

export default AppNavigator;
