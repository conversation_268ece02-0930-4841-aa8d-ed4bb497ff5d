
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MOCK_SKILLS } from '../data/mockData';
import { theme } from '../styles/theme';
import { Skill } from '../types';

const SkillBar: React.FC<{ skill: Skill }> = ({ skill }) => {
  return (
    <View style={styles.skillContainer}>
      <View style={styles.skillHeader}>
        <Text style={styles.skillName}>{skill.name}</Text>
        <Text style={styles.skillLevel}>{skill.userLevel}%</Text>
      </View>
      <View style={styles.barContainer}>
        <View style={[styles.userLevelBar, { width: `${skill.userLevel}%` }]} />
        <View style={[styles.demandMarker, { left: `${skill.demand}%` }]} />
      </View>
    </View>
  );
};

export const SkillTrends: React.FC<{ isDashboard?: boolean }> = ({ isDashboard }) => {
  const skillsToShow = isDashboard ? MOCK_SKILLS.slice(0, 3) : MOCK_SKILLS;
  return (
    <View>
      {skillsToShow.map(skill => (
          <SkillBar key={skill.name} skill={skill} />
      ))}
       <View style={styles.legendContainer}>
          <View style={styles.legendItem}>
              <View style={[styles.legendIndicator, { backgroundColor: theme.colors.brand.accent }]} />
              <Text style={styles.legendText}>Your Level</Text>
          </View>
          <View style={styles.legendItem}>
              <View style={[styles.legendIndicator, styles.demandLegendIndicator]} />
              <Text style={styles.legendText}>Market Demand</Text>
          </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
    skillContainer: {
        marginBottom: 20,
    },
    skillHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    skillName: {
        fontSize: 16,
        fontWeight: 'bold',
        color: theme.colors.text.primary,
    },
    skillLevel: {
        fontSize: 14,
        fontWeight: 'bold',
        color: theme.colors.brand.accent,
    },
    barContainer: {
        height: 12,
        backgroundColor: theme.colors.slate[700],
        borderRadius: theme.borderRadius.full,
        position: 'relative',
    },
    userLevelBar: {
        height: '100%',
        backgroundColor: theme.colors.brand.accent,
        borderRadius: theme.borderRadius.full,
    },
    demandMarker: {
        position: 'absolute',
        top: -4,
        bottom: -4,
        width: 2,
        backgroundColor: theme.colors.sky[300],
    },
    legendContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: 24,
        gap: 24,
    },
    legendItem: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    legendIndicator: {
        width: 12,
        height: 12,
        borderRadius: 3,
        marginRight: 8,
    },
    demandLegendIndicator: {
        width: 2,
        height: 12,
        backgroundColor: theme.colors.sky[300]
    },
    legendText: {
        color: theme.colors.text.secondary,
        fontSize: 12,
    }
});
