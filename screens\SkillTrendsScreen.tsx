
import React from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import { Card, CardHeader } from '../components/ui/Card';
import { theme } from '../styles/theme';
import { SkillTrends } from '../components/SkillTrends';

const SkillTrendsScreen: React.FC = () => {
  return (
    <ScrollView style={styles.container}>
      <Card>
        <CardHeader title="Your Skills vs. Market Demand" />
        <SkillTrends />
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
        backgroundColor: theme.colors.brand.primary,
    },
});

export default SkillTrendsScreen;
