
import React from 'react';
import {
  createD<PERSON>erNavi<PERSON>or,
  DrawerContentScrollView,
  DrawerItemList,
  DrawerItem,
  DrawerContentComponentProps,
} from '@react-navigation/drawer';
import DashboardScreen from '../screens/DashboardScreen';
import AutomationFeedScreen from '../screens/AutomationFeedScreen';
import ApplicationTrackerScreen from '../screens/ApplicationTrackerScreen';
import SkillTrendsScreen from '../screens/SkillTrendsScreen';
import ResumeTemplatesScreen from '../screens/ResumeTemplatesScreen';
import SettingsScreen from '../screens/SettingsScreen';
import { theme } from '../styles/theme';
import { HomeIcon, BriefcaseIcon, ClipboardListIcon, TrendingUpIcon, DocumentDuplicateIcon, CogIcon, LogoutIcon, SparklesIcon } from '../components/ui/icons';
import { View, Text, StyleSheet } from 'react-native';

const Drawer = createDrawerNavigator();

const CustomDrawerContent = (props: DrawerContentComponentProps & { onLogout: () => void; }) => {
    const { onLogout, ...drawerProps } = props;
    return (
        <View style={{ flex: 1, backgroundColor: theme.colors.brand.secondary }}>
            <DrawerContentScrollView {...drawerProps} contentContainerStyle={{ paddingTop: 0 }}>
                <View style={styles.drawerHeader}>
                    <SparklesIcon color={theme.colors.brand.accent} size={32} />
                    <Text style={styles.drawerHeaderText}>PA</Text>
                </View>
                <DrawerItemList {...drawerProps} />
            </DrawerContentScrollView>
            <View style={styles.drawerFooter}>
                <DrawerItem
                    label="Logout"
                    labelStyle={{ color: theme.colors.text.secondary, fontWeight: 'bold' }}
                    onPress={onLogout}
                    icon={({ color, size }) => <LogoutIcon color={theme.colors.text.secondary} size={size} />}
                />
            </View>
        </View>
    );
};


const MainDrawerNavigator = ({ onLogout }: { onLogout: () => void }) => {
  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} onLogout={onLogout} />}
      screenOptions={{
        headerStyle: { backgroundColor: theme.colors.brand.secondary },
        headerTintColor: theme.colors.text.primary,
        drawerStyle: { backgroundColor: theme.colors.brand.secondary },
        drawerActiveBackgroundColor: theme.colors.brand.accent,
        drawerActiveTintColor: theme.colors.brand.primary,
        drawerInactiveTintColor: theme.colors.text.secondary,
        drawerLabelStyle: { marginLeft: -20, fontWeight: 'bold' }
      }}
    >
      <Drawer.Screen name="Dashboard" component={DashboardScreen} options={{ drawerIcon: ({color, size}) => <HomeIcon color={color} size={size}/> }}/>
      <Drawer.Screen name="Automation Feed" component={AutomationFeedScreen} options={{ drawerIcon: ({color, size}) => <BriefcaseIcon color={color} size={size}/> }}/>
      <Drawer.Screen name="Applications" component={ApplicationTrackerScreen} options={{ drawerIcon: ({color, size}) => <ClipboardListIcon color={color} size={size}/> }}/>
      <Drawer.Screen name="Skill Trends" component={SkillTrendsScreen} options={{ drawerIcon: ({color, size}) => <TrendingUpIcon color={color} size={size}/> }}/>
      <Drawer.Screen name="Resume Templates" component={ResumeTemplatesScreen} options={{ drawerIcon: ({color, size}) => <DocumentDuplicateIcon color={color} size={size}/> }}/>
      <Drawer.Screen name="Settings" component={SettingsScreen} options={{ drawerIcon: ({color, size}) => <CogIcon color={color} size={size}/> }}/>
    </Drawer.Navigator>
  );
};

const styles = StyleSheet.create({
    drawerHeader: {
        height: 80,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.slate[700],
        backgroundColor: theme.colors.brand.secondary,
    },
    drawerHeaderText: {
        fontSize: 24,
        fontWeight: 'bold',
        color: theme.colors.text.primary,
        marginLeft: 8,
    },
    drawerFooter: {
        borderTopWidth: 1,
        borderTopColor: theme.colors.slate[700],
        paddingBottom: 20,
    }
})

export default MainDrawerNavigator;
