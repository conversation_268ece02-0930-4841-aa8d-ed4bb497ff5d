
export type Page = 'Dashboard' | 'Jobs' | 'Applications' | 'Skills' | 'ResumeTemplates' | 'Settings';

export enum ApplicationStatus {
  Queued = 'Queued for Tailoring',
  AwaitingApproval = 'Awaiting Approval',
  AutoApplying = 'Auto-Applying',
  Applying = 'Applying',
  Submitted = 'Submitted',
  Interview = 'Interview',
  Offer = 'Offer',
  Rejected = 'Rejected',
}

export interface User {
  name: string;
  email: string;
  avatarUrl: string;
}

export interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  matchScore: number;
  postedAt: string;
  description: string;
  skills: string[];
}

export interface LogEntry {
    timestamp: string;
    message: string;
}

export interface Application {
  id:string;
  job: Job;
  status: ApplicationStatus;
  logs?: LogEntry[];
}

export interface Skill {
  name: string;
  demand: number;
  userLevel: number;
}

export interface ResumeTemplate {
  id: string;
  name:string;
  content: string;
  baseScore: number;
  isDefault: boolean;
  createdAt: string;
}

export interface SuggestedEdit {
    section: string;
    original_bullet: string;
    suggested_bullet: string;
}

export interface TailoredResumeSuggestion {
    ats_score_uplift: number;
    missing_keywords: string[];
    suggested_edits: SuggestedEdit[];
}

export interface SkillRecommendation {
  courseName: string;
  platform: string;
  url: string;
  description: string;
}

export interface CoverLetter {
    content: string;
}

export interface AutoApplyRule {
    id: string;
    field: 'matchScore';
    operator: '>';
    value: number;
}

export interface AutomationSettings {
    autoApplyEnabled: boolean;
    rules: AutoApplyRule[];
}