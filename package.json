{"name": "pa---personal-agent", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "@react-navigation/native": "^7.1.14", "react-native": "^0.80.2", "@google/genai": "^1.11.0", "@react-navigation/native-stack": "^7.3.21", "@react-navigation/drawer": "^7.5.3", "react-native-gesture-handler": "^2.27.2"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}