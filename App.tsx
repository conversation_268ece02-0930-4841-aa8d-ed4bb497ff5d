
import 'react-native-gesture-handler';
import React from 'react';
import { NavigationContainer, Theme } from '@react-navigation/native';
import { ToastProvider } from './contexts/ToastProvider';
import AppNavigator from './navigation/AppNavigator';
import { theme } from './styles/theme';
import { StatusBar } from 'react-native';

const App: React.FC = () => {
  const navigationTheme: Theme & { fonts: any } = {
    dark: true,
    colors: {
      primary: theme.colors.brand.accent,
      background: theme.colors.brand.primary,
      card: theme.colors.brand.secondary,
      text: theme.colors.text.primary,
      border: theme.colors.slate[700],
      notification: theme.colors.brand.accent,
    },
    fonts: {}, // Fix for theme type error
  };

  return (
    <ToastProvider>
      <NavigationContainer theme={navigationTheme}>
        <StatusBar barStyle="light-content" />
        <AppNavigator />
      </NavigationContainer>
    </ToastProvider>
  );
};

export default App;
