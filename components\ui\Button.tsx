
import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View, ViewStyle, TextStyle, ActivityIndicator } from 'react-native';
import { theme } from '../../styles/theme';

interface ButtonProps {
  title?: string;
  onPress?: () => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  children?: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  style,
  textStyle,
  variant = 'primary',
  disabled = false,
  loading = false,
  icon,
  children
}) => {
  const buttonStyles = [
    styles.button,
    variant === 'primary' ? styles.primaryButton : styles.secondaryButton,
    (disabled || loading) && styles.disabledButton,
    style
  ];
  
  const textStyles = [
    styles.text,
    variant === 'primary' ? styles.primaryText : styles.secondaryText,
    icon && title ? { marginLeft: 8 } : {},
    textStyle
  ];

  const content = loading ? (
    <ActivityIndicator color={variant === 'primary' ? theme.colors.brand.primary : theme.colors.text.primary} />
  ) : (
    <>
      {icon}
      {title && <Text style={textStyles}>{title}</Text>}
      {children}
    </>
  );

  return (
    <TouchableOpacity onPress={onPress} style={buttonStyles} disabled={disabled || loading}>
      {content}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  primaryButton: {
    backgroundColor: theme.colors.brand.accent,
  },
  secondaryButton: {
    backgroundColor: theme.colors.slate[700],
  },
  disabledButton: {
    opacity: 0.5,
  },
  text: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  primaryText: {
    color: theme.colors.brand.primary,
  },
  secondaryText: {
    color: theme.colors.text.primary,
  }
});
