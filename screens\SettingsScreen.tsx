
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Switch } from 'react-native';
import { Card, CardHeader } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { AutomationSettings, AutoApplyRule } from '../types';
import { MOCK_AUTOMATION_SETTINGS } from '../data/mockData';
import { theme } from '../styles/theme';

const MOCK_PORTALS = [
    { id: 'linkedin', name: 'LinkedIn', connected: true },
    { id: 'indeed', name: 'Indeed', connected: false },
    { id: 'naukri', name: '<PERSON><PERSON><PERSON>', connected: true },
    { id: 'glassdoor', name: 'Glassdoor', connected: false },
];

const AutomationControls: React.FC = () => {
    const [settings, setSettings] = useState<AutomationSettings>(MOCK_AUTOMATION_SETTINGS);

    const handleToggleAutoApply = (enabled: boolean) => {
        setSettings(s => ({ ...s, autoApplyEnabled: enabled }));
    };

    return (
        <Card>
            <CardHeader title="Automation Controls" />
            <View style={styles.row}>
                <Text style={styles.rowLabel}>Enable Auto-Apply Agent</Text>
                <Switch
                    trackColor={{ false: theme.colors.slate[600], true: theme.colors.brand.accent }}
                    thumbColor={theme.colors.text.primary}
                    onValueChange={handleToggleAutoApply}
                    value={settings.autoApplyEnabled}
                />
            </View>
        </Card>
    );
};

export const SettingsScreen: React.FC = () => {
    return (
        <ScrollView style={styles.container}>
            <AutomationControls />

            <Card style={styles.card}>
                <CardHeader title="Connected Job Portals" />
                 <Text style={styles.description}>
                    Connect accounts to allow PA to work on your behalf.
                </Text>
                {MOCK_PORTALS.map(portal => (
                    <View key={portal.id} style={[styles.row, styles.portalRow]}>
                        <Text style={styles.rowLabel}>{portal.name}</Text>
                        <Button 
                            title={portal.connected ? 'Disconnect' : 'Connect'}
                            onPress={() => {}}
                            variant={portal.connected ? 'secondary' : 'primary'}
                        />
                    </View>
                ))}
            </Card>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
    },
    card: {
        marginTop: 24,
    },
    description: {
        color: theme.colors.text.secondary,
        marginBottom: 16,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 12,
    },
    portalRow: {
        backgroundColor: theme.colors.slate[800],
        paddingHorizontal: 16,
        borderRadius: theme.borderRadius.md,
        marginBottom: 8,
    },
    rowLabel: {
        fontSize: 16,
        color: theme.colors.text.primary,
        fontWeight: '500',
    }
});

export default SettingsScreen;
