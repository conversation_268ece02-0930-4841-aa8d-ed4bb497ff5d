
import { Job, Application, ApplicationStatus, Skill, ResumeTemplate, AutomationSettings, User } from '../types';

export const MOCK_USER: User = {
  name: '<PERSON>',
  email: '<EMAIL>',
  avatarUrl: 'https://i.pravatar.cc/150?u=a042581f4e29026704d',
};

export const MOCK_JOBS: Job[] = [
  {
    id: 'job1',
    title: 'Senior Frontend Engineer',
    company: 'Innovate Inc.',
    location: 'Remote',
    matchScore: 92,
    postedAt: '2 days ago',
    description: 'Innovate Inc. is seeking a Senior Frontend Engineer to build our next-generation user interfaces with React and TypeScript. You will work with a talented team of engineers to design and implement features that will delight our users. Responsibilities include developing new user-facing features, building reusable components, and optimizing applications for maximum speed and scalability. Strong proficiency in JavaScript, TypeScript, React, and state management libraries like Redux or Zustand is required.',
    skills: ['React', 'TypeScript', 'TailwindCSS', 'GraphQL', 'Next.js'],
  },
   {
    id: 'job_auto',
    title: 'Lead React Developer',
    company: 'Synergy Solutions',
    location: 'Remote',
    matchScore: 95,
    postedAt: '1 day ago',
    description: 'We are seeking an expert Lead React Developer to guide our frontend team. Must have 5+ years of experience with React, TypeScript, and state management. You will be responsible for architecture decisions and mentoring junior developers. This is a high-impact role with a competitive salary.',
    skills: ['React', 'TypeScript', 'Redux', 'Jest', 'CI/CD'],
  },
  {
    id: 'job2',
    title: 'Full Stack Developer',
    company: 'DataDriven Co.',
    location: 'New York, NY (Hybrid)',
    matchScore: 85,
    postedAt: '5 days ago',
    description: 'Join DataDriven Co. as a Full Stack Developer. You will be responsible for the full software development life cycle, from conception to deployment. Experience with Node.js, Python, and cloud services (AWS/GCP) is a plus. We are looking for a problem-solver who is passionate about building scalable and reliable systems. Familiarity with database technology such as PostgreSQL and MongoDB is essential.',
    skills: ['Node.js', 'React', 'PostgreSQL', 'AWS', 'Docker'],
  },
   {
    id: 'job3',
    title: 'AI/ML Engineer',
    company: 'Future AI',
    location: 'San Francisco, CA',
    matchScore: 78,
    postedAt: '1 week ago',
    description: 'Future AI is looking for an ML Engineer to work on our core recommendation engine. You will be working with large datasets to train and deploy models that power our product. Required skills include Python, TensorFlow or PyTorch, and experience with cloud-based ML platforms. This role requires a strong understanding of machine learning algorithms and principles.',
    skills: ['Python', 'TensorFlow', 'PyTorch', 'Kubernetes', 'GCP'],
  },
];

export const MOCK_APPLICATIONS: Application[] = [
  { id: 'app1', job: MOCK_JOBS[0], status: ApplicationStatus.Submitted, logs: [ { timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), message: "Application tailored and submitted." }, { timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), message: "Resume tailoring suggestions generated." }, ] },
  { id: 'app_auto', job: MOCK_JOBS[1], status: ApplicationStatus.AutoApplying, logs: [ { timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), message: "Job meets auto-apply criteria. Queued for application." }, ] },
  { id: 'app2', job: MOCK_JOBS[2], status: ApplicationStatus.Interview, logs: [ { timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), message: "Interview scheduled with hiring manager." }, { timestamp: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), message: "Application viewed by recruiter." }, ] },
  { id: 'app3', job: MOCK_JOBS[3], status: ApplicationStatus.Queued, logs: [{timestamp: new Date().toISOString(), message: "Job matched and queued for tailoring."}] },
  { id: 'app4', job: { ...MOCK_JOBS[0], id: 'job4', title: 'React Developer' }, status: ApplicationStatus.AwaitingApproval },
];

export const MOCK_SKILLS: Skill[] = [
  { name: 'Gemini API', demand: 95, userLevel: 60 },
  { name: 'React', demand: 90, userLevel: 85 },
  { name: 'Vector DB', demand: 85, userLevel: 40 },
  { name: 'TailwindCSS', demand: 80, userLevel: 90 },
  { name: 'GraphQL', demand: 75, userLevel: 70 },
  { name: 'Serverless', demand: 70, userLevel: 50 },
];

export const MOCK_RESUME_TEMPLATES: ResumeTemplate[] = [
  {
    id: 'resume1',
    name: 'Standard Tech Resume',
    isDefault: true,
    baseScore: 88,
    createdAt: '2024-05-01',
    content: `
John Doe
Software Engineer
<EMAIL> | (123) 456-7890 | linkedin.com/in/johndoe

Summary:
Experienced Software Engineer with a passion for creating intuitive and performant user experiences. Proficient in web technologies and always eager to learn.

Experience:
Tech Solutions LLC - Software Engineer (Jan 2020 - Present)
- Developed and maintained web applications using JavaScript and React.
- Collaborated with cross-functional teams to define and ship new features.
- Wrote unit tests to ensure code quality.

Education:
B.S. in Computer Science, State University (2016 - 2020)
`
  },
  {
    id: 'resume2',
    name: 'AI/ML Focused Resume',
    isDefault: false,
    baseScore: 85,
    createdAt: '2024-06-15',
    content: `
Jane Doe
AI/ML Engineer
<EMAIL> | (123) 555-7890 | linkedin.com/in/janedoe

Summary:
Innovative AI/ML Engineer with hands-on experience in developing and deploying machine learning models. Skilled in Python, TensorFlow, and PyTorch with a focus on recommendation systems.

Experience:
Data Insights Corp - Machine Learning Intern (Jun 2021 - Dec 2021)
- Worked on a team to improve the accuracy of a content recommendation engine.
- Preprocessed and analyzed large datasets for model training.
- Built and evaluated models using TensorFlow and Scikit-learn.

Projects:
- Personal Movie Recommender: Developed a recommendation system using collaborative filtering on the MovieLens dataset.

Education:
M.S. in Artificial Intelligence, Tech Institute (2020-2022)
`
  },
];

export const MOCK_AUTOMATION_SETTINGS: AutomationSettings = {
    autoApplyEnabled: true,
    rules: [
        { id: 'rule1', field: 'matchScore', operator: '>', value: 90 },
    ],
};