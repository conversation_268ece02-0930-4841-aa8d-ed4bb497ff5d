
import { GoogleGenAI, Type } from "@google/genai";
import { Job, ResumeTemplate, TailoredResumeSuggestion, SkillRecommendation, CoverLetter } from '../types';

// IMPORTANT: This assumes the API key is set in the environment variables.
// Do not hardcode the API key in the code.
const apiKey = process.env.API_KEY;
if (!apiKey) {
  console.warn("API_KEY environment variable not set. Gemini API calls will fail.");
}

const ai = new GoogleGenAI({ apiKey: apiKey || '' });
const model = "gemini-2.5-flash";

const resumeTailoringSchema = {
  type: Type.OBJECT,
  properties: {
    ats_score_uplift: { 
      type: Type.NUMBER,
      description: "An estimated percentage uplift in the ATS match score (e.g., 25 for 25%)."
    },
    missing_keywords: {
      type: Type.ARRAY,
      items: { type: Type.STRING },
      description: "An array of important keywords from the job description that are missing from the resume."
    },
    suggested_edits: {
      type: Type.ARRAY,
      items: {
        type: Type.OBJECT,
        properties: {
          section: { 
            type: Type.STRING,
            description: "The resume section to edit (e.g., 'Summary', 'Experience - Tech Solutions LLC')."
          },
          original_bullet: {
            type: Type.STRING,
            description: "The original bullet point or text from the resume."
          },
          suggested_bullet: {
            type: Type.STRING,
            description: "The improved, tailored bullet point that incorporates keywords and aligns with the job description."
          }
        },
        required: ["section", "original_bullet", "suggested_bullet"]
      }
    }
  },
  required: ["ats_score_uplift", "missing_keywords", "suggested_edits"]
};

const coverLetterSchema = {
    type: Type.OBJECT,
    properties: {
        content: {
            type: Type.STRING,
            description: "The full text of the generated cover letter."
        }
    },
    required: ["content"]
};

const skillRecsSchema = {
    type: Type.ARRAY,
    items: {
        type: Type.OBJECT,
        properties: {
            courseName: { type: Type.STRING, description: "The name of the course or resource." },
            platform: { type: Type.STRING, description: "The platform offering the course (e.g., Coursera, Udemy, official docs)." },
            url: { type: Type.STRING, description: "A direct URL to the course." },
            description: { type: Type.STRING, description: "A brief, one-sentence description of why this resource is useful." }
        },
        required: ["courseName", "platform", "url", "description"]
    }
};

const generateWithSchema = async (prompt: string, schema: object) => {
    if (!apiKey) {
        throw new Error("Gemini API key is not configured.");
    }
    try {
        const response = await ai.models.generateContent({
            model: model,
            contents: prompt,
            config: {
                responseMimeType: "application/json",
                responseSchema: schema,
                temperature: 0.2,
            },
        });
        const jsonText = response.text.trim();
        return JSON.parse(jsonText);
    } catch (error) {
        console.error("Error with Gemini API:", error);
        throw new Error("Failed to get data from AI. Please check your API key and try again.");
    }
};


export const tailorResumeForJob = async (job: Job, resume: ResumeTemplate): Promise<TailoredResumeSuggestion> => {
  const prompt = `
    Analyze the provided job description and base resume. Your goal is to tailor the resume to perfectly match the job, focusing on passing Applicant Tracking Systems (ATS) and impressing recruiters.

    Job Description:
    ---
    Title: ${job.title}
    Company: ${job.company}
    Description: ${job.description}
    Required Skills: ${job.skills.join(', ')}
    ---

    Base Resume:
    ---
    ${resume.content}
    ---

    Based on this, generate a JSON object that includes:
    1.  'ats_score_uplift': An estimated percentage uplift in ATS score.
    2.  'missing_keywords': A list of crucial keywords from the job description missing in the resume.
    3.  'suggested_edits': Specific, actionable edits for the resume. For each edit, provide the section, the original text, and the suggested improved text. Focus on aligning the resume's experience and summary with the job's responsibilities and requirements.
  `;
  return generateWithSchema(prompt, resumeTailoringSchema);
};

export const generateCoverLetter = async (job: Job, resume: ResumeTemplate): Promise<CoverLetter> => {
    const prompt = `
    Based on the following job description and resume, write a professional, concise, and compelling cover letter. The cover letter should be tailored to the specific role and company, highlighting the candidate's most relevant skills and experiences from their resume.

    Job Description:
    ---
    Title: ${job.title} at ${job.company}
    Description: ${job.description}
    ---

    Candidate's Resume:
    ---
    ${resume.content}
    ---

    Generate a JSON object with a single key "content" containing the full cover letter text.
    `;
    return generateWithSchema(prompt, coverLetterSchema);
};

export const getSkillRecommendations = async (skillName: string): Promise<SkillRecommendation[]> => {
    const prompt = `
    I need to improve my skills in "${skillName}". Please recommend 2-3 high-quality online learning resources. For each resource, provide the course name, the platform (e.g., Coursera, Udemy, official documentation), a direct URL, and a brief description.

    Provide the output as a JSON array of objects.
    `;
    return generateWithSchema(prompt, skillRecsSchema);
}
