
import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, SafeAreaView, TouchableOpacity } from 'react-native';
import { Button } from '../components/ui/Button';
import { theme } from '../styles/theme';
import { SparklesIcon } from '../components/ui/icons';

const LoginScreen = ({ navigation, onLoginSuccess }: { navigation: any, onLoginSuccess: () => void }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleLogin = () => {
    // In a real app, you'd validate credentials against a backend.
    console.log('Logging in with:', email, password);
    onLoginSuccess();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
            <SparklesIcon size={60} color={theme.colors.brand.accent} />
            <Text style={styles.title}>Personal Agent</Text>
            <Text style={styles.subtitle}>Sign in to continue</Text>
        </View>

        <TextInput
          style={styles.input}
          placeholder="Email"
          placeholderTextColor={theme.colors.text.secondary}
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />
        <TextInput
          style={styles.input}
          placeholder="Password"
          placeholderTextColor={theme.colors.text.secondary}
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />
        
        <Button title="Login" onPress={handleLogin} style={styles.button} />

        <View style={styles.footer}>
          <Text style={styles.footerText}>Don't have an account? </Text>
          <TouchableOpacity onPress={() => navigation.navigate('Register')}>
            <Text style={styles.linkText}>Sign Up</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.brand.primary,
    justifyContent: 'center',
  },
  content: {
    paddingHorizontal: 30,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.colors.text.primary,
    marginTop: 16,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.text.secondary,
    marginTop: 4,
  },
  input: {
    backgroundColor: theme.colors.brand.secondary,
    color: theme.colors.text.primary,
    padding: 15,
    borderRadius: theme.borderRadius.md,
    marginBottom: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: theme.colors.slate[600],
  },
  button: {
    marginTop: 20,
  },
  footer: {
    marginTop: 30,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    color: theme.colors.text.secondary,
    fontSize: 14,
  },
  linkText: {
    color: theme.colors.brand.accent,
    fontWeight: 'bold',
    fontSize: 14,
  }
});

export default LoginScreen;