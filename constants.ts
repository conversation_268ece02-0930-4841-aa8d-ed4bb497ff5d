
import { ApplicationStatus, Page } from './types';
import { HomeIcon, BriefcaseIcon, ClipboardListIcon, TrendingUpIcon, CogIcon, DocumentDuplicateIcon } from './components/ui/icons';

export const NAV_ITEMS: { id: Page; name: string; icon: React.FC<React.SVGProps<SVGSVGElement>> }[] = [
  { id: 'Dashboard', name: 'Dashboard', icon: HomeIcon },
  { id: 'Jobs', name: 'Job Matches', icon: BriefcaseIcon },
  { id: 'Applications', name: 'Applications', icon: ClipboardListIcon },
  { id: 'Skills', name: 'Skill Trends', icon: TrendingUpIcon },
  { id: 'ResumeTemplates', name: 'Resume Templates', icon: DocumentDuplicateIcon },
  { id: 'Settings', name: 'Settings', icon: CogIcon },
];

export const KANBAN_ORDER: ApplicationStatus[] = [
  ApplicationStatus.Queued,
  ApplicationStatus.AwaitingApproval,
  ApplicationStatus.AutoApplying,
  ApplicationStatus.Applying,
  ApplicationStatus.Submitted,
  ApplicationStatus.Interview,
  ApplicationStatus.Offer,
];
