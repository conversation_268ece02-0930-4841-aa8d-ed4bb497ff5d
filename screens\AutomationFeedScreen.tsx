
import React, { useState, useMemo, useCallback } from 'react';
import { View, Text, StyleSheet, FlatList, Modal, ActivityIndicator, ScrollView, SafeAreaView, TextInput, TouchableOpacity } from 'react-native';
import { MOCK_JOBS, MOCK_RESUME_TEMPLATES, MOCK_AUTOMATION_SETTINGS } from '../data/mockData';
import { Job, TailoredResumeSuggestion, ResumeTemplate, CoverLetter, AutomationSettings } from '../types';
import { Card, CardHeader } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { tailorResumeForJob, generateCoverLetter } from '../services/geminiService';
import { theme } from '../styles/theme';
import { SparklesIcon, RobotIcon, CloseIcon } from '../components/ui/icons';
import { useToast } from '../contexts/ToastProvider';

const checkAutoApplyCriteria = (job: Job, settings: AutomationSettings): boolean => {
    if (!settings.autoApplyEnabled) return false;
    return settings.rules.every(rule => {
        if (rule.field === 'matchScore' && rule.operator === '>') {
            return job.matchScore > rule.value;
        }
        return false;
    });
};

const TailorResumeModal: React.FC<{ job: Job; visible: boolean; onClose: () => void; }> = ({ job, visible, onClose }) => {
    const [suggestion, setSuggestion] = useState<TailoredResumeSuggestion | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const defaultResume = useMemo(() => MOCK_RESUME_TEMPLATES.find(r => r.isDefault), []);
    const toast = useToast();

    const fetchSuggestions = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        if (!defaultResume) {
            setError("No default resume template found.");
            setIsLoading(false);
            return;
        }
        try {
            const result = await tailorResumeForJob(job, defaultResume);
            setSuggestion(result);
        } catch (err) {
            setError(err instanceof Error ? err.message : "An unknown error occurred.");
        } finally {
            setIsLoading(false);
        }
    }, [job, defaultResume]);
    
    React.useEffect(() => {
        if(visible) {
            fetchSuggestions();
        }
    }, [visible, fetchSuggestions]);

    const handleApprove = () => {
        toast.show({ type: 'success', message: `Application for ${job.title} submitted!` });
        onClose();
    }

    return (
        <Modal
            animationType="slide"
            transparent={true}
            visible={visible}
            onRequestClose={onClose}
        >
            <View style={styles.modalContainer}>
                <SafeAreaView style={{flex: 1}}>
                <Card style={styles.modalCard}>
                    <View style={styles.modalHeader}>
                        <Text style={styles.modalTitle}>Tailor Resume</Text>
                        <TouchableOpacity onPress={onClose}><CloseIcon color={theme.colors.text.secondary} /></TouchableOpacity>
                    </View>
                    <ScrollView>
                        <Text style={styles.modalJobTitle}>{job.title}</Text>
                         {isLoading && <ActivityIndicator size="large" color={theme.colors.brand.accent} style={{marginVertical: 20}} />}
                         {error && <Text style={styles.errorText}>Error: {error}</Text>}
                         {suggestion && (
                             <View>
                                <Text style={styles.sectionTitle}>Missing Keywords</Text>
                                <View style={styles.keywordContainer}>
                                    {suggestion.missing_keywords.map(kw => <Text key={kw} style={styles.keyword}>{kw}</Text>)}
                                </View>
                                <Text style={styles.sectionTitle}>Suggested Edits</Text>
                                {suggestion.suggested_edits.map((edit, i) => (
                                    <View key={i} style={styles.editCard}>
                                        <Text style={styles.editSection}>{edit.section}</Text>
                                        <Text style={styles.editOriginal}>{edit.original_bullet}</Text>
                                        <Text style={styles.editSuggested}>{edit.suggested_bullet}</Text>
                                    </View>
                                ))}
                             </View>
                         )}
                    </ScrollView>
                    <View style={styles.modalFooter}>
                        <Button title="Close" onPress={onClose} variant="secondary" />
                        <Button title="Approve & Submit" onPress={handleApprove} />
                    </View>
                </Card>
                </SafeAreaView>
            </View>
        </Modal>
    );
};


const AutomationFeedScreen: React.FC = () => {
    const [selectedJob, setSelectedJob] = useState<Job | null>(null);
    const toast = useToast();

    const handleQueueForAutoApply = (job: Job) => {
        toast.show({ type: 'success', message: `Queued "${job.title}" for auto-apply.` });
    };

    const renderJob = ({ item }: { item: Job }) => {
        const canAutoApply = checkAutoApplyCriteria(item, MOCK_AUTOMATION_SETTINGS);
        return (
            <Card style={styles.jobCard}>
                <View style={{flex: 1}}>
                    <View style={styles.jobCardHeader}>
                        <Text style={styles.jobCardTitle}>{item.title}</Text>
                        <Text style={styles.matchScoreText}>{item.matchScore}%</Text>
                    </View>
                    <Text style={styles.jobCardCompany}>{item.company} - {item.location}</Text>
                    <Text style={styles.jobCardDescription} numberOfLines={3}>{item.description}</Text>
                </View>
                <View style={styles.jobCardFooter}>
                    {canAutoApply ? (
                         <Button 
                            title="Queue Auto-Apply" 
                            onPress={() => handleQueueForAutoApply(item)} 
                            icon={<RobotIcon color={theme.colors.brand.primary} size={18}/>}
                         />
                    ) : (
                        <Button 
                            title="Tailor & Apply" 
                            onPress={() => setSelectedJob(item)}
                            icon={<SparklesIcon color={theme.colors.brand.primary} size={18}/>}
                        />
                    )}
                </View>
            </Card>
        );
    }

    return (
        <View style={styles.container}>
            <FlatList
                data={MOCK_JOBS}
                renderItem={renderJob}
                keyExtractor={item => item.id}
                contentContainerStyle={{ padding: 16 }}
            />
            {selectedJob && <TailorResumeModal job={selectedJob} visible={!!selectedJob} onClose={() => setSelectedJob(null)} />}
        </View>
    );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.brand.primary,
  },
  jobCard: {
    marginBottom: 16,
  },
  jobCardHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 4,
  },
  jobCardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text.primary,
    flex: 1,
  },
  matchScoreText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.green[400],
    backgroundColor: 'rgba(49, 196, 141, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: theme.borderRadius.md,
    marginLeft: 8,
  },
  jobCardCompany: {
    fontSize: 14,
    color: theme.colors.text.secondary,
    marginBottom: 8,
  },
  jobCardDescription: {
      fontSize: 14,
      color: theme.colors.text.secondary,
      lineHeight: 20,
  },
  jobCardFooter: {
      marginTop: 16,
      borderTopWidth: 1,
      borderTopColor: theme.colors.slate[700],
      paddingTop: 16,
  },
  modalContainer: {
      flex: 1,
      justifyContent: 'flex-end',
      backgroundColor: 'rgba(0,0,0,0.7)',
  },
  modalCard: {
      height: '90%',
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.slate[700],
  },
  modalTitle: {
      fontSize: 22,
      fontWeight: 'bold',
      color: theme.colors.text.primary
  },
  modalJobTitle: {
    fontSize: 18,
    color: theme.colors.brand.accent,
    fontWeight: 'bold',
    marginVertical: 16,
  },
  sectionTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text.secondary,
      marginTop: 16,
      marginBottom: 8,
  },
  keywordContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
  },
  keyword: {
      backgroundColor: theme.colors.sky[900],
      color: theme.colors.sky[300],
      paddingHorizontal: 10,
      paddingVertical: 5,
      borderRadius: 15,
      margin: 4,
      fontSize: 12,
  },
  editCard: {
      backgroundColor: theme.colors.slate[800],
      borderRadius: theme.borderRadius.md,
      padding: 12,
      marginBottom: 12,
  },
  editSection: {
      fontWeight: 'bold',
      color: theme.colors.brand.accent,
      marginBottom: 8,
  },
  editOriginal: {
      color: theme.colors.red[400],
      textDecorationLine: 'line-through',
      fontSize: 12,
      marginBottom: 4,
  },
  editSuggested: {
      color: theme.colors.green[400],
      fontSize: 12,
  },
  modalFooter: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingTop: 16,
      borderTopWidth: 1,
      borderTopColor: theme.colors.slate[700],
  },
  errorText: {
      color: theme.colors.red[400],
      textAlign: 'center',
      margin: 20,
  }
});

export default AutomationFeedScreen;